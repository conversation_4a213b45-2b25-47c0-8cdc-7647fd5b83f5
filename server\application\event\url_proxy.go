package event

import (
	"socks/server/domain/entity"
	urlService "socks/server/domain/service"
	"sync"
)

var (
	urlProxy *URLProxy
	upOnce   sync.Once
)

type URLProxy struct {
	urlProxyService *urlService.URLProxyService
}

func GetURLProxy() *URLProxy {
	upOnce.Do(func() {
		urlProxy = &URLProxy{
			urlProxyService: urlService.NewURLProxyService(),
		}
	})
	return urlProxy
}

func (p *URLProxy) RegisterURLMapping(clientUUID string, urlPath string, targetURL string, serviceName string) error {
	return p.urlProxyService.RegisterURLMapping(clientUUID, urlPath, targetURL, serviceName)
}

func (p *URLProxy) UnregisterURLMapping(clientUUID string, urlPath string) error {
	return p.urlProxyService.UnregisterURLMapping(clientUUID, urlPath)
}

func (p *URLProxy) GetURLMapping(urlPath string) *entity.URLMapping {
	return p.urlProxyService.GetURLMapping(urlPath)
}

func (p *URLProxy) GetClientURLMappings(clientUUID string) []string {
	return p.urlProxyService.GetClientURLMappings(clientUUID)
}

func (p *URLProxy) UpdateOnlineStatus(clientUUID string, online bool) error {
	return p.urlProxyService.UpdateOnlineStatus(clientUUID, online)
}
