package handlers

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"socks/server/application/event"
	"socks/server/domain/entity"
	"sync"
	"time"
)

type URLProxyHandler struct {
	urlProxy        *event.URLProxy
	tunnels         *entity.TunnelGroup
	responseWaiters map[string]chan *entity.URLProxyMessage // 等待响应的通道
	waitersLock     sync.RWMutex
}

func GetURLProxyHandler() *URLProxyHandler {
	return &URLProxyHandler{
		urlProxy:        event.GetURLProxy(),
		tunnels:         entity.GetTunnelGroup(),
		responseWaiters: make(map[string]chan *entity.URLProxyMessage),
	}
}

// RegisterURLHandler 处理URL注册请求
func (h *URLProxyHandler) RegisterURLHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 解析请求参数
	clientUUID := r.URL.Query().Get("client_id")
	if clientUUID == "" {
		http.Error(w, "missing client_id", http.StatusBadRequest)
		return
	}

	urlPath := r.URL.Query().Get("url_path")
	if urlPath == "" {
		http.Error(w, "missing url_path", http.StatusBadRequest)
		return
	}

	targetURL := r.URL.Query().Get("target_url")
	if targetURL == "" {
		http.Error(w, "missing target_url", http.StatusBadRequest)
		return
	}

	serviceName := r.URL.Query().Get("service_name")
	if serviceName == "" {
		serviceName = "unknown"
	}

	// 注册URL映射
	err := h.urlProxy.RegisterURLMapping(clientUUID, urlPath, targetURL, serviceName)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// 返回成功响应
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success":      true,
		"url_path":     urlPath,
		"target_url":   targetURL,
		"service_name": serviceName,
	})

	log.Printf("URL映射注册成功: %s -> %s (客户端: %s)", urlPath, targetURL, clientUUID)
}

// UnregisterURLHandler 处理URL取消注册请求
func (h *URLProxyHandler) UnregisterURLHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodDelete {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 解析请求参数
	clientUUID := r.URL.Query().Get("client_id")
	if clientUUID == "" {
		http.Error(w, "missing client_id", http.StatusBadRequest)
		return
	}

	urlPath := r.URL.Query().Get("url_path")
	if urlPath == "" {
		http.Error(w, "missing url_path", http.StatusBadRequest)
		return
	}

	// 取消注册URL映射
	err := h.urlProxy.UnregisterURLMapping(clientUUID, urlPath)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// 返回成功响应
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success":  true,
		"url_path": urlPath,
	})

	log.Printf("URL映射取消注册成功: %s (客户端: %s)", urlPath, clientUUID)
}

// ProxyHandler 处理代理请求
func (h *URLProxyHandler) ProxyHandler(w http.ResponseWriter, r *http.Request) {
	// 获取请求路径
	urlPath := r.URL.Path

	// 查找URL映射
	mapping := h.urlProxy.GetURLMapping(urlPath)
	if mapping == nil {
		http.Error(w, "URL mapping not found", http.StatusNotFound)
		return
	}

	// 检查映射是否在线
	if !mapping.Online {
		http.Error(w, "Service offline", http.StatusServiceUnavailable)
		return
	}

	// 获取客户端连接
	tunnel := h.tunnels.GetTunnel(mapping.Client.UUID)
	if tunnel == nil {
		http.Error(w, "Client not connected", http.StatusServiceUnavailable)
		return
	}

	// 构建代理请求消息
	proxyRequest := entity.URLProxyMessage{
		ID:        generateRequestID(),
		Type:      "proxy_request",
		URLPath:   urlPath,
		TargetURL: mapping.TargetURL,
		Method:    r.Method,
		Headers:   make(map[string]string),
		Body:      nil,
	}

	// 复制请求头
	for name, values := range r.Header {
		if len(values) > 0 {
			proxyRequest.Headers[name] = values[0]
		}
	}

	// 读取请求体
	if r.Body != nil {
		body, err := io.ReadAll(r.Body)
		if err != nil {
			http.Error(w, "Failed to read request body", http.StatusInternalServerError)
			return
		}
		proxyRequest.Body = body
	}

	// 序列化代理请求
	proxyData, err := json.Marshal(proxyRequest)
	if err != nil {
		http.Error(w, "Failed to serialize proxy request", http.StatusInternalServerError)
		return
	}

	// 构建消息
	message := entity.ConnMessage{
		ID:   proxyRequest.ID,
		Type: "proxy_request",
		Data: proxyData,
	}

	// 发送代理请求到客户端
	err = tunnel.WriteJSON(message)
	if err != nil {
		http.Error(w, "Failed to send request to client", http.StatusInternalServerError)
		return
	}

	// 创建响应等待通道
	responseChan := make(chan *entity.URLProxyMessage, 1)
	h.waitersLock.Lock()
	h.responseWaiters[proxyRequest.ID] = responseChan
	h.waitersLock.Unlock()

	// 等待客户端响应（30秒超时）
	select {
	case response := <-responseChan:
		// 清理等待通道
		h.waitersLock.Lock()
		delete(h.responseWaiters, proxyRequest.ID)
		h.waitersLock.Unlock()

		// 处理响应
		if response.Error != "" {
			http.Error(w, response.Error, http.StatusInternalServerError)
			return
		}

		// 设置响应头
		for name, value := range response.Headers {
			w.Header().Set(name, value)
		}

		// 设置状态码
		w.WriteHeader(response.Status)

		// 写入响应体
		if len(response.Body) > 0 {
			w.Write(response.Body)
		}

		log.Printf("代理请求完成: %s -> %s (状态码: %d)", urlPath, mapping.TargetURL, response.Status)

	case <-time.After(30 * time.Second):
		// 清理等待通道
		h.waitersLock.Lock()
		delete(h.responseWaiters, proxyRequest.ID)
		h.waitersLock.Unlock()

		http.Error(w, "Request timeout", http.StatusGatewayTimeout)
		log.Printf("代理请求超时: %s -> %s", urlPath, mapping.TargetURL)
	}
}

// generateRequestID 生成请求ID
func generateRequestID() string {
	// 简单的ID生成，实际应用中可以使用UUID
	return fmt.Sprintf("req_%d", time.Now().UnixNano())
}
