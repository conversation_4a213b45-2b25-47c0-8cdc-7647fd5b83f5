package service

import (
	"fmt"
	"log"
	"socks/server/domain/entity"
	"time"
)

type URLProxyService struct {
	urlMappings *entity.URLMappingGroup
	clients     *entity.ClientGroup
}

func NewURLProxyService() *URLProxyService {
	return &URLProxyService{
		urlMappings: entity.GetURLMappingGroup(),
		clients:     entity.GetClientGroup(),
	}
}

// RegisterURLMapping 注册URL映射
func (s *URLProxyService) RegisterURLMapping(clientUUID string, urlPath string, targetURL string, serviceName string) error {
	// 检查客户端是否存在
	client := s.clients.GetClient(clientUUID)
	if client == nil {
		return fmt.Errorf("client not found: %s", clientUUID)
	}

	// 检查URL路径是否已被注册
	existingMapping := s.urlMappings.GetMapping(urlPath)
	if existingMapping != nil {
		return fmt.Errorf("URL path already registered: %s", urlPath)
	}

	// 创建URL映射
	mapping := &entity.URLMapping{
		Name:        fmt.Sprintf("%s:%s", client.Name, urlPath),
		Client:      client,
		URLPath:     urlPath,
		TargetURL:   targetURL,
		Created:     time.Now(),
		Enable:      true,
		Online:      true,
		ServiceName: serviceName,
		Description: fmt.Sprintf("URL mapping for %s", serviceName),
	}

	// 添加映射
	s.urlMappings.AddMapping(urlPath, mapping)
	
	log.Printf("URL映射注册成功: %s -> %s (客户端: %s)", urlPath, targetURL, clientUUID)
	return nil
}

// UnregisterURLMapping 取消注册URL映射
func (s *URLProxyService) UnregisterURLMapping(clientUUID string, urlPath string) error {
	// 检查映射是否存在
	mapping := s.urlMappings.GetMapping(urlPath)
	if mapping == nil {
		return fmt.Errorf("URL mapping not found: %s", urlPath)
	}

	// 检查是否属于该客户端
	if mapping.Client.UUID != clientUUID {
		return fmt.Errorf("URL mapping does not belong to client: %s", clientUUID)
	}

	// 删除映射
	s.urlMappings.DeleteMapping(urlPath)
	
	log.Printf("URL映射取消注册成功: %s (客户端: %s)", urlPath, clientUUID)
	return nil
}

// GetURLMapping 获取URL映射
func (s *URLProxyService) GetURLMapping(urlPath string) *entity.URLMapping {
	return s.urlMappings.GetMapping(urlPath)
}

// GetClientURLMappings 获取客户端的所有URL映射
func (s *URLProxyService) GetClientURLMappings(clientUUID string) []string {
	return s.urlMappings.GetClientPaths(clientUUID)
}

// UpdateOnlineStatus 更新在线状态
func (s *URLProxyService) UpdateOnlineStatus(clientUUID string, online bool) error {
	s.urlMappings.UpdateOnlineStatusByClientStatus(clientUUID, online)
	return nil
}
