# URL路径代理功能使用说明

## 功能概述

新增的URL路径代理功能允许通过URL路径将请求转发到客户端的具体应用，与现有的端口映射功能并行存在。

### 现有功能（端口映射）
- 访问 `server:port` 直接转发到 `client:localPort`
- 基于端口的代理转发

### 新增功能（URL路径映射）
- 访问 `server:apiPort/path` 转发到客户端的具体应用
- 基于URL路径的代理转发

## 使用流程

### 1. 启动服务

#### 启动Server端
```bash
cd server
go run main.go -config=path/to/config.json
```

#### 启动Client端
```bash
cd client
go run main.go -server=<server_ip> -port=<server_port> -manager=8090
```

### 2. 注册URL映射

客户端提供HTTP接口供其他应用注册URL映射：

```bash
# 注册URL映射
curl -X POST "http://localhost:8090/url/register?url_path=/api/v1/service&target_url=http://localhost:8080/api/v1/service&service_name=my_service"

# 响应示例
{
  "success": true,
  "url_path": "/api/v1/service",
  "target_url": "http://localhost:8080/api/v1/service",
  "service_name": "my_service"
}
```

### 3. 使用代理

注册成功后，可以通过server端访问：

```bash
# 通过server端访问注册的URL
curl "http://<server_ip>:<api_port>/api/v1/service"
```

请求流程：
1. 请求发送到 `server:apiPort/api/v1/service`
2. Server查找URL映射，找到对应的客户端
3. Server将请求转发给客户端
4. 客户端向本地应用 `http://localhost:8080/api/v1/service` 发起请求
5. 客户端将响应返回给server
6. Server将响应返回给原始请求者

### 4. 取消注册URL映射

```bash
# 取消注册URL映射
curl -X DELETE "http://localhost:8090/url/unregister?url_path=/api/v1/service"

# 响应示例
{
  "success": true,
  "url_path": "/api/v1/service"
}
```

## API接口说明

### Client端接口

#### 注册URL映射
- **URL**: `POST /url/register`
- **参数**:
  - `url_path`: URL路径（如 `/api/v1/service`）
  - `target_url`: 目标URL（如 `http://localhost:8080/api/v1/service`）
  - `service_name`: 服务名称（可选）

#### 取消注册URL映射
- **URL**: `DELETE /url/unregister`
- **参数**:
  - `url_path`: 要取消注册的URL路径

### Server端接口

#### URL代理
- **URL**: `<任意路径>`
- **说明**: Server会自动查找注册的URL映射并转发请求

## 配置说明

### Server端配置
在现有配置基础上，URL代理功能使用相同的 `ManagerPort` 作为API端口。

### Client端配置
使用 `-manager` 参数指定客户端API服务端口（默认8090）。

## 示例场景

### 场景1：微服务代理
```bash
# 客户端A注册用户服务
curl -X POST "http://localhost:8090/url/register?url_path=/api/users&target_url=http://localhost:3001/api/users&service_name=user_service"

# 客户端B注册订单服务  
curl -X POST "http://localhost:8091/url/register?url_path=/api/orders&target_url=http://localhost:3002/api/orders&service_name=order_service"

# 外部访问
curl "http://server:port/api/users"    # 转发到客户端A的用户服务
curl "http://server:port/api/orders"   # 转发到客户端B的订单服务
```

### 场景2：开发环境代理
```bash
# 注册开发环境的前端应用
curl -X POST "http://localhost:8090/url/register?url_path=/app&target_url=http://localhost:3000&service_name=frontend_dev"

# 外部访问开发环境
curl "http://server:port/app"  # 转发到本地开发服务器
```

## 注意事项

1. **URL路径唯一性**: 每个URL路径只能被一个客户端注册
2. **客户端连接**: 客户端必须保持与server的连接才能接收代理请求
3. **超时设置**: HTTP请求默认超时时间为30秒
4. **并发处理**: 支持并发处理多个代理请求
5. **错误处理**: 当目标服务不可用时，会返回相应的错误信息

## 故障排除

### 常见问题

1. **URL映射注册失败**
   - 检查URL路径是否已被其他客户端注册
   - 确认客户端已成功连接到server

2. **代理请求失败**
   - 检查目标URL是否可访问
   - 确认客户端服务正常运行
   - 查看客户端和server端日志

3. **连接断开**
   - 检查网络连接
   - 确认server端配置正确
   - 重启客户端重新建立连接
