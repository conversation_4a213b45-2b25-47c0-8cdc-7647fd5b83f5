package main

import (
	"bufio"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"math/rand"
	"net"
	"net/http"
	dao "socks/server/infra/dao"
	repo "socks/server/infra/repo"
	"socks/server/server/handlers"
	util "socks/server/util"
	"strconv"
	"sync"
	"time"
)

var (
	// itDao = dao.GetIntranetTunnelDaoImpl()
	// 获取系统配置
	sysConfig *util.TunnelConfig
	itDao     = dao.GetIntranetTunnelDaoImpl()
)

// Message 定义了 TCP/UDP 上传输的消息格式，用于多路复用
type Message struct {
	ID   string `json:"id"`             // 连接 ID
	Type string `json:"type"`           // open, data, close
	Data []byte `json:"data,omitempty"` // 二进制数据
}

// SafeConn 包装 TCP 连接，保证并发写安全
type SafeConn struct {
	Conn net.Conn
	wmu  sync.Mutex
	enc  *json.Encoder
	dec  *json.Decoder
	bw   *bufio.Writer // 添加缓冲写入器
}

// PortMapping 存储端口映射信息
type PortMapping struct {
	ID          int          // 数据库记录id
	PC          int          // 本地端口
	PS          int          // 公网端口
	ClientName  string       // 客户端名称
	ClientIP    string       // 客户端IP地址
	CientUUID   string       // 客户端唯一id
	ClientType  string       // 端口对应的
	Listener    net.Listener // 监听器
	Created     time.Time    // 创建时间
	Online      bool         // 是否在线
	ServiceName string       // 具体端口对应的服务名称，不可为空
}

func (c *SafeConn) WriteJSON(v interface{}) error {
	c.wmu.Lock()
	defer c.wmu.Unlock()
	err := c.enc.Encode(v)
	// 对所有消息类型都立即刷新缓冲区
	if err == nil && c.bw != nil {
		err = c.bw.Flush()
	}
	return err
}

func (c *SafeConn) ReadJSON(v interface{}) error {
	return c.dec.Decode(v)
}

func (c *SafeConn) Close() error {
	return c.Conn.Close()
}

var (
	// 持有每个 ps 对应的 TCP 连接
	tcpConns    = make(map[int]*SafeConn)
	tcpConnsMux sync.RWMutex

	// 存放每个连接 ID 对应的响应通道
	respChans = make(map[string]chan []byte)
	respMu    sync.Mutex

	// 全局端口映射列表
	portMappings    = make(map[int]*PortMapping) // 公网端口 -> 映射信息
	portMappingsMux sync.RWMutex

	// 已分配的端口集合
	allocatedPorts    = make(map[int]bool) // 公网端口 -> 是否已分配
	allocatedPortsMux sync.Mutex

	// 客户端映射缓存 (clientUUID+localPort -> 服务端端口)
	clientPortCache    = make(map[string]int)
	clientPortCacheMux sync.RWMutex

	// 缓存过期时间(默认30分钟)
	portCacheExpiration = 30 * time.Minute
)

// 分配一个未被占用的随机端口
func allocateRandomPort() (int, error) {
	// 最多尝试20次
	for i := 0; i < 20; i++ {
		allocatedPortsMux.Lock()
		// 使用配置的端口范围生成随机端口
		portRange := sysConfig.MaxPort - sysConfig.MinPort
		ps := rand.Intn(portRange) + sysConfig.MinPort

		// 检查端口是否已被分配
		if allocatedPorts[ps] {
			allocatedPortsMux.Unlock()
			continue
		}

		// 尝试监听该端口，检查是否可用
		ln, err := net.Listen("tcp", fmt.Sprintf("0.0.0.0:%d", ps))
		if err != nil {
			allocatedPortsMux.Unlock()
			log.Printf("端口 %d 不可用: %v", ps, err)
			continue
		}
		defer ln.Close()

		// 标记端口为已分配
		allocatedPorts[ps] = true
		allocatedPortsMux.Unlock()

		return ps, nil
	}

	return 0, fmt.Errorf("无法分配可用端口，请稍后再试")
}

// 释放端口
func releasePort(ps int) {
	portMappingsMux.Lock()
	if mapping, exists := portMappings[ps]; exists {
		if mapping.Listener != nil {
			mapping.Listener.Close()
		}
		delete(portMappings, ps)
	}
	portMappingsMux.Unlock()

	allocatedPortsMux.Lock()
	delete(allocatedPorts, ps)
	allocatedPortsMux.Unlock()

	log.Printf("端口 %d 已释放", ps)
}

// 从数据库加载隧道记录，初始化缓存和自增ID
func loadTunnelRecordsFromDB() {
	// 获取所有隧道记录
	tunnels, err := itDao.GetAll()
	if err != nil {
		log.Printf("加载隧道记录失败: %v", err)
		return
	}

	log.Printf("从数据库加载了 %d 条隧道记录", len(tunnels))

	// 处理每条记录
	clientPortCacheMux.Lock()
	defer clientPortCacheMux.Unlock()
	allocatedPortsMux.Lock()
	defer allocatedPortsMux.Unlock()

	for _, tunnel := range tunnels {

		// 构建缓存键
		cacheKey := fmt.Sprintf("%s:%d", tunnel.UUID, tunnel.ClientPort)

		// 添加到缓存
		clientPortCache[cacheKey] = tunnel.ServerPort

		// 标记服务端口为已分配
		allocatedPorts[tunnel.ServerPort] = true

		log.Printf("缓存记录: UUID=%s, 本地端口=%d, 服务端口=%d",
			tunnel.UUID, tunnel.ClientPort, tunnel.ServerPort)
	}

}

// 尝试恢复启用的端口映射
func restoreEnabledPortMappings() {
	// 获取所有启用的隧道记录
	tunnels, err := itDao.GetEnabled()
	if err != nil {
		log.Printf("加载启用的隧道记录失败: %v", err)
		return
	}

	log.Printf("尝试恢复 %d 个启用的端口映射", len(tunnels))

	for _, tunnel := range tunnels {
		// 尝试监听该端口
		ln, err := net.Listen("tcp", fmt.Sprintf("0.0.0.0:%d", tunnel.ServerPort))
		if err != nil {
			log.Printf("无法恢复端口 %d: %v", tunnel.ServerPort, err)
			continue
		}

		// 创建映射信息
		portMapping := &PortMapping{
			PC:         tunnel.ClientPort,
			PS:         tunnel.ServerPort,
			ClientName: tunnel.ClientName,
			ClientIP:   tunnel.ClientIp,
			CientUUID:  tunnel.UUID,
			Listener:   ln,
			Created:    tunnel.CreateTime,
		}

		// 记录映射信息
		portMappingsMux.Lock()
		portMappings[tunnel.ServerPort] = portMapping
		portMappingsMux.Unlock()

		log.Printf("恢复端口映射: 本地端口=%d, 服务端口=%d, UUID=%s",
			tunnel.ClientPort, tunnel.ServerPort, tunnel.UUID)

		// 启动监听
		go handleListener(tunnel.ServerPort, ln)
	}
}

func updateOnlineStatus(ps int) {
	portMappingsMux.Lock()
	tcpConnsMux.RLock()
	if mapping, exists := portMappings[ps]; exists {
		_, hasConnection := tcpConns[ps]
		mapping.Online = hasConnection
		itDao.UpdateOnlineStatus(mapping.ID, mapping.Online)

		if hasConnection {
			itDao.UpdateLastConnectionTime(mapping.ID)
		}
	}
	tcpConnsMux.RUnlock()
	portMappingsMux.Unlock()
}

func main() {
	// 设置详细日志
	log.SetFlags(log.Ldate | log.Ltime | log.Lmicroseconds | log.Lshortfile)

	// 使用配置的管理端口，但允许通过命令行参数覆盖
	configPath := flag.String("config", "", "配置文件")
	flag.Parse()

	sysConfig = util.GetSystemConfig(*configPath)

	apiPort := strconv.Itoa(sysConfig.ManagerPort)

	// 使用配置的过期时间（天转换为分钟）
	defaultCacheExpiration := sysConfig.SlidingExpiration * 24 * 60 // 天转换为分钟
	cacheExpiration := flag.Int("cache", defaultCacheExpiration, "端口缓存过期时间(分钟)")

	// 设置缓存过期时间
	if *cacheExpiration > 0 {
		portCacheExpiration = time.Duration(*cacheExpiration) * time.Minute
	}

	// 初始化随机数生成器
	rand.New(rand.NewSource(time.Now().UnixNano()))

	// 启动缓存清理协程
	go cleanExpiredCache(portCacheExpiration)

	go loadTunnelRecordsFromDB()
	go restoreEnabledPortMappings()

	// Start HTTP server for allocation API
	log.Printf("HTTP API server listening on port %s", apiPort)
	log.Printf("Server configuration: Port range %d-%d, Max connections %d, Timeout %ds",
		sysConfig.MinPort, sysConfig.MaxPort, sysConfig.MaxConnection, sysConfig.Timeout)
	log.Printf("Port cache expiration: %v", portCacheExpiration)

	http.HandleFunc("/allocate", allocateHandler)
	http.HandleFunc("/status", statusHandler)
	http.HandleFunc("/tunnel/refresh", refreshHandler)
	http.HandleFunc("/connection", connectionHandler)

	// 新增的
	http.HandleFunc("/register", handlers.GetPortProxyHandler().RegisterHandler)
	http.HandleFunc("/allocate_port", handlers.GetPortProxyHandler().AllocateHandler)

	log.Fatal(http.ListenAndServe("0.0.0.0:"+apiPort, nil))
}

// 显示当前端口映射状态
func statusHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	mappings := []*PortMapping{}

	portMappingsMux.RLock()
	for _, mapping := range portMappings {
		mappings = append(mappings, mapping)
	}
	portMappingsMux.RUnlock()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"mappings": mappings,
		"count":    len(mappings),
	})
}

// 分配一个公网端口，并为该 pc 启动监听
func allocateHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 获取参数
	pcStr := r.URL.Query().Get("pc")
	if pcStr == "" {
		http.Error(w, "missing pc", http.StatusBadRequest)
		return
	}
	pc, err := strconv.Atoi(pcStr)
	if err != nil {
		http.Error(w, "invalid pc", http.StatusBadRequest)
		return
	}

	// 获取客户端上报的IP地址
	clientIP := r.URL.Query().Get("client_ip")
	if clientIP == "" {
		// 如果客户端没有上报IP，使用备用方法
		clientIP = r.RemoteAddr
		// 如果有代理，尝试获取真实IP
		if forwardedFor := r.Header.Get("X-Forwarded-For"); forwardedFor != "" {
			clientIP = forwardedFor
		}
		// 去除端口部分
		if host, _, err := net.SplitHostPort(clientIP); err == nil {
			clientIP = host
		}
	}

	clientUUID := r.URL.Query().Get("id")
	if clientUUID == "" {
		http.Error(w, "invalid id", http.StatusBadRequest)
		return
	}

	clientName := r.URL.Query().Get("name")

	clientType := r.URL.Query().Get("type")
	serviceName := r.URL.Query().Get("service_name")

	// 检查缓存中是否有相同UUID+本地端口的映射
	cacheKey := fmt.Sprintf("%s:%d", clientUUID, pc)
	clientPortCacheMux.Lock()
	cachedPS, exists := clientPortCache[cacheKey]
	clientPortCacheMux.Unlock()

	var ps int

	if exists {
		// 检查缓存的端口是否仍然可用
		portMappingsMux.Lock()
		mapping, portExists := portMappings[cachedPS]
		portMappingsMux.Unlock()

		if portExists && mapping.CientUUID == clientUUID && mapping.PC == pc {
			// 如果端口仍然存在且映射信息匹配，复用该端口
			ps = cachedPS
			log.Printf("复用缓存端口 ps=%d 给 pc=%d (客户端UUID: %s)", ps, pc, clientUUID)

			// 更新响应
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(map[string]int{"ps": ps})
			return
		}
	}

	// 分配随机端口
	ps, err = allocateRandomPort()
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// 创建监听器
	ln, err := net.Listen("tcp", fmt.Sprintf("0.0.0.0:%d", ps))
	if err != nil {
		// 释放端口并返回错误
		releasePort(ps)
		http.Error(w, "listen error", http.StatusInternalServerError)
		return
	}

	// 记录映射信息
	portMapping := &PortMapping{
		PC:          pc,
		PS:          ps,
		ClientName:  clientName,
		ClientIP:    clientIP,
		CientUUID:   clientUUID,
		ClientType:  clientType,
		Listener:    ln,
		Created:     time.Now(),
		ServiceName: serviceName,
	}

	portMappingsMux.Lock()
	portMappings[ps] = portMapping
	portMappingsMux.Unlock()

	// 更新缓存
	clientPortCacheMux.Lock()
	clientPortCache[cacheKey] = ps
	clientPortCacheMux.Unlock()

	go func() {
		id, err := itDao.Create(portMapping.ToRepo())
		if err != nil {
			log.Printf("create conn info error: %v", err)
		}
		portMapping.ID = id
	}()

	log.Printf("分配端口 ps=%d 给 pc=%d (客户端UUID: %s, IP: %s)", ps, pc, clientUUID, clientIP)

	// 启动监听器处理连接
	go handleListener(ps, ln)

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"ps": ps,
	})
}

// 从数据库中刷新端口映射信息
func refreshHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 定义请求体结构
	var reqBody struct {
		TunnelIDs  []int `json:"tunnel_ids"`
		RefreshAll bool  `json:"refresh_all"`
	}

	// 解析请求体
	if err := json.NewDecoder(r.Body).Decode(&reqBody); err != nil {
		http.Error(w, "Invalid request body: "+err.Error(), http.StatusBadRequest)
		return
	}

	var tunnels []*repo.IntranetTunnel
	var err error

	// 根据 refresh_all 参数决定刷新方式
	if reqBody.RefreshAll {
		tunnels, err = itDao.GetAll()
		if err != nil {
			http.Error(w, "Failed to get all tunnels: "+err.Error(), http.StatusInternalServerError)
			return
		}
	} else if len(reqBody.TunnelIDs) > 0 {
		// 获取指定ID的隧道
		tunnelPtrs, err := itDao.GetIntranetTunnelsByIDs(reqBody.TunnelIDs)
		if err != nil {
			http.Error(w, "Failed to get tunnels by IDs: "+err.Error(), http.StatusInternalServerError)
			return
		}
		// 转换指针切片为值切片
		tunnels = make([]*repo.IntranetTunnel, len(tunnelPtrs))
		copy(tunnels, tunnelPtrs)
	} else {
		http.Error(w, "No tunnel IDs provided and refresh_all is false", http.StatusBadRequest)
		return
	}

	// 更新端口映射
	for _, tunnel := range tunnels {
		// 检查端口是否已分配
		portMappingsMux.Lock()
		if mapping, exists := portMappings[tunnel.ServerPort]; exists {
			// 更新现有映射
			mapping.PC = tunnel.ClientPort
			mapping.PS = tunnel.ServerPort
			mapping.ClientName = tunnel.ClientName
			mapping.ClientIP = tunnel.ClientIp
			mapping.CientUUID = tunnel.UUID
			mapping.Created = tunnel.CreateTime
		} else {
			// 尝试创建新的监听器
			ln, err := net.Listen("tcp", fmt.Sprintf("0.0.0.0:%d", tunnel.ServerPort))
			if err != nil {
				log.Printf("无法为端口 %d 创建监听器: %v", tunnel.ServerPort, err)
				continue
			}

			// 创建新的映射
			portMappings[tunnel.ServerPort] = &PortMapping{
				PC:         tunnel.ClientPort,
				PS:         tunnel.ServerPort,
				ClientName: tunnel.ClientName,
				ClientIP:   tunnel.ClientIp,
				CientUUID:  tunnel.UUID,
				Listener:   ln,
				Created:    tunnel.CreateTime,
			}

			// 启动监听器
			go handleListener(tunnel.ServerPort, ln)
		}
		portMappingsMux.Unlock()

		// 更新缓存
		cacheKey := fmt.Sprintf("%s:%d", tunnel.UUID, tunnel.ClientPort)
		clientPortCacheMux.Lock()
		clientPortCache[cacheKey] = tunnel.ServerPort
		clientPortCacheMux.Unlock()
	}

	// 返回更新结果
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
	})
}

// connectionHandler 处理控制连接请求
func connectionHandler(w http.ResponseWriter, r *http.Request) {
	// 只接受GET请求，后续会升级为TCP连接
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 获取端口参数
	psStr := r.URL.Query().Get("ps")
	if psStr == "" {
		http.Error(w, "missing ps", http.StatusBadRequest)
		return
	}

	ps, err := strconv.Atoi(psStr)
	if err != nil {
		http.Error(w, "invalid ps", http.StatusBadRequest)
		return
	}

	// 检查端口是否已分配
	portMappingsMux.Lock()
	_, exists := portMappings[ps]
	portMappingsMux.Unlock()

	if !exists {
		http.Error(w, "Port not allocated", http.StatusNotFound)
		return
	}

	// 升级HTTP连接为TCP连接
	hj, ok := w.(http.Hijacker)
	if !ok {
		http.Error(w, "Hijacking not supported", http.StatusInternalServerError)
		return
	}

	conn, bufrw, err := hj.Hijack()
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// 发送HTTP 101状态码表示切换协议
	response := "HTTP/1.1 101 Switching Protocols\r\n" +
		"Upgrade: tcp\r\n" +
		"Connection: Upgrade\r\n\r\n"

	if _, err := bufrw.WriteString(response); err != nil {
		conn.Close()
		log.Printf("写入升级响应失败: %v", err)
		return
	}

	if err := bufrw.Flush(); err != nil {
		conn.Close()
		log.Printf("刷新缓冲区失败: %v", err)
		return
	}

	log.Printf("连接已升级为TCP: port=%d", ps)

	// 创建SafeConn
	bufWriter := bufio.NewWriterSize(conn, 32*1024)
	sconn := &SafeConn{
		Conn: conn,
		bw:   bufWriter,
		enc:  json.NewEncoder(bufWriter),
		dec:  json.NewDecoder(bufio.NewReaderSize(conn, 32*1024)),
	}

	// 优化TCP连接参数
	if tcpConn, ok := conn.(*net.TCPConn); ok {
		tcpConn.SetNoDelay(true)   // 禁用Nagle算法
		tcpConn.SetKeepAlive(true) // 启用TCP keepalive
		tcpConn.SetKeepAlivePeriod(30 * time.Second)
		tcpConn.SetWriteBuffer(64 * 1024) // 增大写缓冲区
		tcpConn.SetReadBuffer(64 * 1024)  // 增大读缓冲区
	}

	// 存储连接
	tcpConnsMux.Lock()
	tcpConns[ps] = sconn
	tcpConnsMux.Unlock()

	// 更新隧道在线状态
	updateOnlineStatus(ps)

	log.Printf("控制连接已建立: ps=%d, remote=%s", ps, conn.RemoteAddr())

	// 处理连接
	handleControlConn(sconn, ps)
}

// 监听公网端口，接受新连接并转发
func handleListener(ps int, ln net.Listener) {
	defer func() {
		ln.Close()
		releasePort(ps)
	}()

	log.Printf("Listening on port %d", ps)
	for {
		conn, err := ln.Accept()
		if err != nil {
			log.Printf("Accept error on ps=%d: %v", ps, err)
			return
		}

		log.Printf("接受新连接 from %s on port %d", conn.RemoteAddr(), ps)

		tcpConnsMux.Lock()
		sconn := tcpConns[ps]
		tcpConnsMux.Unlock()

		if sconn == nil {
			log.Printf("No TCP for ps=%d", ps)
			conn.Close()
			continue
		}
		log.Println("开始转发数据")
		go proxyConn(conn, sconn, ps)
	}
}

// proxyConn 将 TCP <-> TCP 数据双向转发，使用多路复用 ID
func proxyConn(tcpConn net.Conn, ctrl *SafeConn, ps int) {
	defer tcpConn.Close()
	// Generate unique ID
	id := fmt.Sprintf("%d-%d", time.Now().UnixNano(), tcpConn.RemoteAddr().(*net.TCPAddr).Port)

	log.Printf("New TCP connection established: %s -> localhost:%d", tcpConn.RemoteAddr(), ps)
	// Create response channel
	respMu.Lock()
	ch := make(chan []byte, 100)
	respChans[id] = ch
	respMu.Unlock()

	// Notify client to open local connection
	ctrl.WriteJSON(Message{ID: id, Type: "open"})
	// 确保消息立即发送
	if bw, ok := ctrl.Conn.(interface{ Flush() error }); ok {
		bw.Flush()
	}
	log.Printf("Control channel notified to open connection: %s", id)

	// TCP -> Control channel
	go func() {
		buf := make([]byte, 4096)
		for {
			n, err := tcpConn.Read(buf)
			if n > 0 {
				ctrl.WriteJSON(Message{ID: id, Type: "data", Data: buf[:n]})
				// 确保数据消息立即发送
				if bw, ok := ctrl.Conn.(interface{ Flush() error }); ok {
					bw.Flush()
				}
			}
			if err != nil {
				break
			}
		}
		ctrl.WriteJSON(Message{ID: id, Type: "close"})
		// 确保关闭消息立即发送
		if bw, ok := ctrl.Conn.(interface{ Flush() error }); ok {
			bw.Flush()
		}
	}()

	// Control channel -> TCP
	for data := range ch {
		tcpConn.Write(data)
	}

	// Cleanup
	respMu.Lock()
	delete(respChans, id)
	respMu.Unlock()
	log.Printf("Connection %s closed", id)
}

// Replace handleWS with handleControlConn
func handleControlConn(conn *SafeConn, ps int) {
	defer func() {
		conn.Close()
		tcpConnsMux.Lock()
		delete(tcpConns, ps)
		tcpConnsMux.Unlock()
		updateOnlineStatus(ps)
		log.Printf("TCP control connection closed for ps=%d", ps)
	}()

	log.Printf("开始处理控制连接 ps=%d", ps)

	// 创建更大的缓冲区以提高性能
	bufWriter := bufio.NewWriterSize(conn.Conn, 32*1024)
	conn.bw = bufWriter
	conn.enc = json.NewEncoder(bufWriter)

	// 设置TCP连接参数以减少延迟
	if tcpConn, ok := conn.Conn.(*net.TCPConn); ok {
		tcpConn.SetNoDelay(true) // 禁用Nagle算法，减少延迟
	}

	for {
		var msg Message
		if err := conn.ReadJSON(&msg); err != nil {
			log.Printf("TCP read error: %v", err)
			break
		}

		// 记录消息接收时间，用于监控延迟
		recvTime := time.Now()
		log.Printf("收到客户端消息: type=%s, id=%s, dataLen=%d, time=%s",
			msg.Type, msg.ID, len(msg.Data), recvTime.Format("15:04:05.000"))

		// 所有消息类型都立即处理
		switch msg.Type {
		case "ping":
			response := Message{
				ID:   msg.ID,
				Type: "pong",
				Data: []byte("pong"),
			}
			if err := conn.WriteJSON(response); err != nil {
				log.Printf("发送pong响应失败: %v", err)
				return
			}
		case "data":
			if ch := getResponseChannel(msg.ID); ch != nil {
				select {
				case ch <- msg.Data: // 非阻塞发送
				default:
					log.Printf("警告: 通道已满，消息可能丢失: id=%s", msg.ID)
				}
			}
		case "close":
			closeResponseChannel(msg.ID)
		default:
			log.Printf("未知消息类型: %s", msg.Type)
		}
	}
}

func (portInfo *PortMapping) FromRepo(obj *repo.IntranetTunnel) {
	portInfo.ID = obj.ID
	portInfo.ClientName = obj.ClientName
	portInfo.CientUUID = obj.UUID
	portInfo.ClientIP = obj.ClientIp
	portInfo.ClientType = obj.ClientType
	portInfo.Created = obj.CreateTime
	portInfo.PC = obj.ClientPort
	portInfo.PS = obj.ServerPort
	portInfo.Online = obj.Online
	portInfo.ServiceName = obj.ServiceName
}

func (portInfo *PortMapping) ToRepo() *repo.IntranetTunnel {
	return &repo.IntranetTunnel{
		UUID:               portInfo.CientUUID,
		Name:               portInfo.ClientName + ":" + strconv.Itoa(portInfo.PC),
		ClientName:         portInfo.ClientName,
		ClientIp:           portInfo.ClientIP,
		Protocol:           "tcp",
		ServerPort:         portInfo.PS,
		ClientPort:         portInfo.PC,
		Enable:             true,
		Encryption:         false,
		CreateTime:         portInfo.Created,
		LastConnectionTime: time.Now(),
		Online:             portInfo.Online,
		ClientType:         portInfo.ClientType,
		ServiceName:        portInfo.ServiceName,
	}
}

// 添加清理过期缓存的函数
func cleanExpiredCache(defaultCacheExpiration time.Duration) {
	for {
		time.Sleep(defaultCacheExpiration)

		now := time.Now()
		clientPortCacheMux.Lock()
		portMappingsMux.Lock()

		for cacheKey, ps := range clientPortCache {
			if mapping, exists := portMappings[ps]; exists {
				// 检查映射是否过期
				if now.Sub(mapping.Created) > portCacheExpiration {
					// 只从缓存中删除，不释放端口
					// 端口释放由handleListener负责
					delete(clientPortCache, cacheKey)
				}
			} else {
				// 如果映射不存在，从缓存中删除
				delete(clientPortCache, cacheKey)
			}
		}

		portMappingsMux.Unlock()
		clientPortCacheMux.Unlock()
	}
}

func getResponseChannel(id string) chan []byte {
	respMu.Lock()
	defer respMu.Unlock()
	return respChans[id]
}

func closeResponseChannel(id string) {
	respMu.Lock()
	defer respMu.Unlock()
	if ch, ok := respChans[id]; ok {
		close(ch)
		delete(respChans, id)
	}
}
