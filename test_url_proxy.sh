#!/bin/bash

# URL代理功能测试脚本

echo "=== URL代理功能测试 ==="

# 配置
CLIENT_API="http://localhost:8090"
SERVER_API="http://localhost:8080"  # 假设server的API端口是8080
TEST_URL_PATH="/api/test"
TEST_TARGET_URL="http://localhost:3000/api/test"
TEST_SERVICE_NAME="test_service"

echo "1. 测试客户端API连接..."
if curl -s "$CLIENT_API/status" > /dev/null; then
    echo "✓ 客户端API连接正常"
else
    echo "✗ 客户端API连接失败，请确保客户端正在运行"
    exit 1
fi

echo ""
echo "2. 注册URL映射..."
REGISTER_RESPONSE=$(curl -s -X POST "$CLIENT_API/url/register?url_path=$TEST_URL_PATH&target_url=$TEST_TARGET_URL&service_name=$TEST_SERVICE_NAME")
echo "注册响应: $REGISTER_RESPONSE"

if echo "$REGISTER_RESPONSE" | grep -q '"success":true'; then
    echo "✓ URL映射注册成功"
else
    echo "✗ URL映射注册失败"
    exit 1
fi

echo ""
echo "3. 等待5秒让映射生效..."
sleep 5

echo ""
echo "4. 测试代理请求..."
echo "尝试访问: $SERVER_API$TEST_URL_PATH"
PROXY_RESPONSE=$(curl -s -w "HTTP_CODE:%{http_code}" "$SERVER_API$TEST_URL_PATH")
echo "代理响应: $PROXY_RESPONSE"

echo ""
echo "5. 取消注册URL映射..."
UNREGISTER_RESPONSE=$(curl -s -X DELETE "$CLIENT_API/url/unregister?url_path=$TEST_URL_PATH")
echo "取消注册响应: $UNREGISTER_RESPONSE"

if echo "$UNREGISTER_RESPONSE" | grep -q '"success":true'; then
    echo "✓ URL映射取消注册成功"
else
    echo "✗ URL映射取消注册失败"
fi

echo ""
echo "=== 测试完成 ==="

echo ""
echo "手动测试命令："
echo "# 注册URL映射"
echo "curl -X POST \"$CLIENT_API/url/register?url_path=/api/hello&target_url=http://localhost:8000/hello&service_name=hello_service\""
echo ""
echo "# 测试代理访问"
echo "curl \"$SERVER_API/api/hello\""
echo ""
echo "# 取消注册"
echo "curl -X DELETE \"$CLIENT_API/url/unregister?url_path=/api/hello\""
echo ""
echo "# 查看客户端状态"
echo "curl \"$CLIENT_API/status\""
